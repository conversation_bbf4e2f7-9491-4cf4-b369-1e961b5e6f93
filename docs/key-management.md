# DataLock Key Management

This document outlines the cryptographic key management strategy for DataLock license signing and verification.

## Overview

DataLock uses ECDSA P-256 (secp256r1) for license token signing and verification:
- **Private Key**: Used by the license service to sign license tokens
- **Public Key**: Embedded in the desktop application for offline license verification

## Key Generation

### Production Environment

For production, private keys MUST be stored in a Hardware Security Module (HSM) or Key Management Service (KMS):

**Recommended Options:**
- **AWS CloudHSM** - Dedicated hardware security module
- **AWS KMS** - Managed key service with FIPS 140-2 Level 2 validation
- **Azure Key Vault** - Microsoft's managed HSM service
- **Google Cloud KMS** - Google's key management service

### Development Environment

For development and testing, keys can be generated locally using the provided scripts.

**⚠️ WARNING: Never use development keys in production!**

## Key Rotation Strategy

1. **Key Versioning**: Each key pair has a version identifier
2. **Gradual Migration**: New licenses use new key, old licenses remain valid
3. **Application Updates**: New public keys distributed via app updates
4. **Revocation**: Old keys can be revoked after sufficient adoption

## Security Requirements

### Private Key Protection
- Must be stored in HSM/KMS with access logging
- Access restricted to license service only
- Multi-factor authentication for key access
- Regular access audits

### Public Key Distribution
- Embedded in application binary during build
- Signed application updates ensure authenticity
- Multiple public keys supported for rotation

## Implementation

### License Service Integration

```python
# Example: AWS KMS integration
import boto3

class LicenseSigningService:
    def __init__(self, kms_key_id: str):
        self.kms = boto3.client('kms')
        self.key_id = kms_key_id
    
    def sign_license(self, license_data: dict) -> str:
        # Sign license using KMS
        pass
```

### Desktop Application Verification

```rust
// Example: Embedded public key verification
use p256::{ecdsa::VerifyingKey, PublicKey};

const LICENSE_PUBLIC_KEY: &str = "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----";

fn verify_license(token: &str) -> Result<LicenseData, LicenseError> {
    // Verify license signature
    pass
}
```

## Key Backup and Recovery

1. **Secure Backup**: Private keys backed up to encrypted offline storage
2. **Split Knowledge**: Backup encryption requires multiple key holders
3. **Recovery Testing**: Regular recovery drills to ensure process works
4. **Documentation**: Detailed recovery procedures documented

## Compliance and Auditing

- All key operations logged with timestamps and user identification
- Regular security audits of key management procedures
- Compliance with relevant standards (SOC 2, ISO 27001)
- Incident response plan for key compromise

## Development Scripts

Use the scripts in `scripts/crypto/` for development key management:

```bash
# Generate development key pair
npm run crypto:generate-dev-keys

# Verify key pair
npm run crypto:verify-keys

# Sign test license
npm run crypto:sign-test-license
```

## Production Deployment Checklist

- [ ] HSM/KMS configured and tested
- [ ] Private key generated in HSM (never exported)
- [ ] Public key extracted and verified
- [ ] License service configured with HSM access
- [ ] Desktop application built with correct public key
- [ ] Key rotation procedures documented
- [ ] Backup and recovery procedures tested
- [ ] Access controls and monitoring configured
- [ ] Security audit completed
