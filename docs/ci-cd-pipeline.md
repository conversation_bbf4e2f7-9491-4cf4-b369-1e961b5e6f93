# DataLock CI/CD Pipeline Documentation

## Overview

The DataLock CI/CD pipeline is designed to ensure code quality, security, and reliable deployments across all components of the system. The pipeline uses GitHub Actions for automation and supports multi-platform builds for the desktop application.

## Pipeline Structure

### 1. Continuous Integration (CI)

**Trigger Events:**
- Push to `main` or `develop` branches
- Pull requests to `main` or `develop` branches

**Jobs:**

#### Lint and Format (`lint-and-format`)
- **Purpose**: Code quality and consistency checks
- **Steps**:
  - Checkout code
  - Setup Node.js environment
  - Install dependencies
  - Check code formatting with Prettier
  - Run ESLint for code quality
  - TypeScript type checking
- **Artifacts**: None
- **Duration**: ~2-3 minutes

#### Test Web Apps (`test-web`)
- **Purpose**: Test Next.js web platform and shared packages
- **Steps**:
  - Checkout code
  - Setup Node.js environment
  - Install dependencies
  - Run Jest/Vitest test suites
  - Generate coverage reports
- **Artifacts**: Test coverage reports
- **Duration**: ~3-5 minutes

#### Test License Service (`test-license-service`)
- **Purpose**: Test FastAPI backend with database integration
- **Services**: PostgreSQL test database
- **Steps**:
  - Checkout code
  - Setup Python environment
  - Start PostgreSQL service
  - Install Python dependencies
  - Run database migrations
  - Execute pytest test suite
- **Artifacts**: Test coverage reports
- **Duration**: ~4-6 minutes

#### Build Desktop App (`build-desktop`)
- **Purpose**: Multi-platform desktop application builds
- **Matrix Strategy**:
  - Ubuntu (Linux AppImage)
  - Windows (MSI installer)
  - macOS (DMG, both Intel and Apple Silicon)
- **Steps**:
  - Checkout code
  - Setup Node.js and Rust environments
  - Install platform-specific dependencies
  - Build Tauri application
  - Upload build artifacts
- **Artifacts**: Platform-specific installers
- **Duration**: ~8-15 minutes per platform

### 2. Release Pipeline (`release`)

**Trigger Events:**
- Git tags matching pattern `v*` (e.g., `v1.0.0`)

**Jobs:**

#### Create Release (`create-release`)
- **Purpose**: Create GitHub release draft
- **Steps**:
  - Extract version from git tag
  - Create draft release on GitHub
  - Set release metadata
- **Outputs**: Release upload URL and version

#### Build and Sign (`build-and-sign`)
- **Purpose**: Build, sign, and upload release artifacts
- **Matrix Strategy**: Same as CI build matrix
- **Steps**:
  - Checkout code
  - Setup build environments
  - Build applications
  - **Code Signing**:
    - Windows: Authenticode signing with certificate
    - macOS: Developer ID signing and notarization
    - Linux: GPG signing of packages
  - Generate SHA-256 checksums
  - Upload signed artifacts to release

## Security and Signing

### Code Signing Certificates

#### Windows
- **Certificate Type**: Extended Validation (EV) Code Signing
- **Storage**: Azure Key Vault or hardware token
- **Process**: Automated signing during build
- **Verification**: Windows SmartScreen compatibility

#### macOS
- **Certificate Type**: Developer ID Application
- **Process**: 
  1. Sign application bundle
  2. Create DMG installer
  3. Sign DMG
  4. Submit for notarization
  5. Staple notarization ticket
- **Requirements**: Apple Developer account, notarization service

#### Linux
- **Method**: GPG signing of packages
- **Distribution**: Signed checksums and detached signatures
- **Verification**: Public key distribution for verification

### Secrets Management

**Required Secrets:**
```yaml
# Tauri signing (for auto-updates)
TAURI_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----..."
TAURI_KEY_PASSWORD: "password"

# Windows code signing
WINDOWS_CERTIFICATE: "base64-encoded-certificate"
WINDOWS_CERTIFICATE_PASSWORD: "certificate-password"

# macOS signing and notarization
APPLE_CERTIFICATE: "base64-encoded-certificate"
APPLE_CERTIFICATE_PASSWORD: "certificate-password"
APPLE_SIGNING_IDENTITY: "Developer ID Application: Company Name"
APPLE_ID: "<EMAIL>"
APPLE_PASSWORD: "app-specific-password"
APPLE_TEAM_ID: "TEAM123456"

# License service deployment
LICENSE_SERVICE_DEPLOY_KEY: "ssh-private-key"
DATABASE_URL: "postgresql://..."
SECRET_KEY: "license-service-secret"

# HSM/KMS access
AWS_ACCESS_KEY_ID: "AKIA..."
AWS_SECRET_ACCESS_KEY: "secret"
KMS_KEY_ID: "arn:aws:kms:..."
```

## Deployment Strategies

### Web Platform
- **Platform**: Vercel
- **Strategy**: Automatic deployment on merge to `main`
- **Environment**: Production environment variables
- **Rollback**: Instant rollback via Vercel dashboard

### License Service
- **Platform**: Fly.io or similar container platform
- **Strategy**: Blue-green deployment
- **Database**: Managed PostgreSQL with migrations
- **Health Checks**: HTTP health endpoints
- **Rollback**: Previous container version

### Desktop Application
- **Distribution**: GitHub Releases
- **Auto-updates**: Tauri updater with signed manifests
- **Channels**: Stable and beta release channels
- **Rollback**: Version pinning and manual rollback

## Quality Gates

### Pre-merge Requirements
- [ ] All CI checks pass
- [ ] Code review approved
- [ ] No security vulnerabilities detected
- [ ] Test coverage above threshold (80%)
- [ ] Documentation updated

### Release Requirements
- [ ] All tests pass on target platforms
- [ ] Security scan completed
- [ ] Code signing successful
- [ ] Checksums generated and verified
- [ ] Release notes prepared
- [ ] Rollback plan documented

## Monitoring and Alerting

### Build Monitoring
- **Metrics**: Build success rate, duration, artifact size
- **Alerts**: Failed builds, long build times
- **Dashboard**: GitHub Actions insights

### Deployment Monitoring
- **Health Checks**: Application health endpoints
- **Metrics**: Response time, error rate, throughput
- **Alerts**: Service degradation, deployment failures
- **Tools**: Prometheus, Grafana, PagerDuty

## Environment Configuration

### Development
```yaml
Environment: development
Database: Local PostgreSQL
Keys: Development ECDSA keys
Signing: Self-signed certificates
Monitoring: Local logging only
```

### Staging
```yaml
Environment: staging
Database: Staging PostgreSQL
Keys: Staging ECDSA keys (HSM)
Signing: Test certificates
Monitoring: Full monitoring stack
```

### Production
```yaml
Environment: production
Database: Production PostgreSQL (HA)
Keys: Production ECDSA keys (HSM)
Signing: Production certificates
Monitoring: Full monitoring + alerting
```

## Troubleshooting

### Common Issues

#### Build Failures
- **Dependency conflicts**: Clear cache, update lockfiles
- **Platform-specific issues**: Check system dependencies
- **Signing failures**: Verify certificate validity

#### Test Failures
- **Database connection**: Check service configuration
- **Flaky tests**: Implement retry logic
- **Environment differences**: Standardize test environments

#### Deployment Issues
- **Health check failures**: Verify application startup
- **Database migration errors**: Check migration scripts
- **Certificate expiry**: Monitor certificate validity

### Debug Commands

```bash
# Local build debugging
npm run build:debug
npm run test:verbose

# Container debugging
docker run -it --entrypoint /bin/bash image:tag

# Database debugging
psql $DATABASE_URL -c "SELECT version();"

# Certificate verification
openssl x509 -in cert.pem -text -noout
```

## Maintenance

### Regular Tasks
- [ ] Update dependencies monthly
- [ ] Rotate signing certificates annually
- [ ] Review and update security policies
- [ ] Performance optimization reviews
- [ ] Disaster recovery testing

### Monitoring
- [ ] Build pipeline performance
- [ ] Security vulnerability scanning
- [ ] Certificate expiry monitoring
- [ ] Resource usage optimization
