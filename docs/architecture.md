# DataLock Architecture v1

## System Overview

DataLock is a secure, offline-first password manager built on the principle of zero-knowledge architecture. The system consists of three main components that work together to provide secure password management without compromising user privacy.

## Core Principles

1. **Zero-Knowledge**: Vault data never leaves the user's device unencrypted
2. **Offline-First**: Core functionality works without internet connection
3. **Cryptographic Security**: All data protected with industry-standard encryption
4. **Signed Licenses**: Offline license validation using cryptographic signatures

## System Components

### 1. Web Platform (`apps/web`)
- **Technology**: Next.js 14, TypeScript, Tailwind CSS
- **Purpose**: Marketing, sales, and download distribution
- **Features**:
  - Landing page with security explanations
  - Pricing and plan selection
  - Payment processing integration
  - Download page with checksums
  - Customer support portal

### 2. License Service (`apps/license-service`)
- **Technology**: FastAPI, Python, PostgreSQL
- **Purpose**: License generation and management
- **Features**:
  - Payment webhook processing
  - ECDSA license token signing
  - Customer and transaction management
  - CRL (Certificate Revocation List) endpoint
  - Admin dashboard for license management

### 3. Desktop Application (`apps/desktop`)
- **Technology**: <PERSON><PERSON>, <PERSON>act, TypeScript, Rust
- **Purpose**: Secure password vault management
- **Features**:
  - Offline license validation
  - SQLCipher encrypted database
  - Master password-based encryption
  - Password generation and management
  - Import/export functionality
  - Auto-lock and security features

## Data Flow

### Purchase to License Flow
```mermaid
sequenceDiagram
    participant U as User
    participant W as Web Platform
    participant P as Payment Provider
    participant L as License Service
    participant E as Email Service

    U->>W: Select plan & checkout
    W->>P: Process payment
    P->>L: Payment webhook
    L->>L: Generate signed license
    L->>E: Send license email
    E->>U: License token + download links
```

### License Validation Flow
```mermaid
sequenceDiagram
    participant D as Desktop App
    participant L as License Service
    participant V as Vault

    D->>D: Verify license signature (offline)
    D->>L: Check CRL (optional, cached)
    D->>D: Validate device limit
    D->>V: Unlock vault if valid
```

## Security Architecture

### Cryptographic Components

1. **License Signing**:
   - Algorithm: ECDSA P-256
   - Private key stored in HSM/KMS
   - Public key embedded in application

2. **Vault Encryption**:
   - Master key derivation: Argon2id
   - Vault encryption: AES-256-GCM or XChaCha20-Poly1305
   - Database: SQLCipher with application-level AEAD

3. **Key Management**:
   - Master Key (MK): Derived from user password via KDF
   - Vault Key (VK): Random 256-bit key, wrapped with MK
   - Each entry encrypted with unique nonce

### Security Boundaries

```
┌─────────────────────────────────────────────────────────────┐
│                        User Device                          │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                Desktop Application                      ││
│  │  ┌─────────────────┐  ┌─────────────────────────────────┐││
│  │  │   UI Layer      │  │         Crypto Core             │││
│  │  │   (React)       │  │         (Rust)                  │││
│  │  │                 │  │  • License validation          │││
│  │  │                 │  │  • KDF (Argon2id)              │││
│  │  │                 │  │  • AEAD encryption             │││
│  │  └─────────────────┘  └─────────────────────────────────┘││
│  │                                                         ││
│  │  ┌─────────────────────────────────────────────────────┐││
│  │  │            SQLCipher Database                       │││
│  │  │            (Encrypted at rest)                      │││
│  │  └─────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                      Cloud Services                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐│
│  │  Web Platform   │  │ License Service │  │   Database      ││
│  │   (Next.js)     │  │   (FastAPI)     │  │ (PostgreSQL)    ││
│  │                 │  │                 │  │                 ││
│  │ • Marketing     │  │ • License gen   │  │ • Customers     ││
│  │ • Sales         │  │ • Webhooks      │  │ • Transactions  ││
│  │ • Downloads     │  │ • CRL           │  │ • Licenses      ││
│  └─────────────────┘  └─────────────────┘  └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Database Schema

### Core Tables

1. **customers**: User account information
2. **plans**: Pricing tiers and features
3. **transactions**: Payment records
4. **licenses**: Issued licenses with tokens
5. **download_events**: Download tracking
6. **admin_users**: Administrative access

### Relationships

```sql
customers (1) ──── (N) transactions
customers (1) ──── (N) licenses
plans (1) ──── (N) licenses
transactions (1) ──── (1) licenses
licenses (1) ──── (N) download_events
```

## Deployment Architecture

### Production Environment

```
┌─────────────────────────────────────────────────────────────┐
│                         CDN                                 │
│                    (Cloudflare)                             │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer                            │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────────────┐    ┌───────────────┐    ┌───────────────┐
│ Web Platform  │    │License Service│    │   Database    │
│  (Vercel)     │    │  (Fly.io)     │    │(Managed PG)   │
└───────────────┘    └───────────────┘    └───────────────┘
                              │
                    ┌───────────────┐
                    │   HSM/KMS     │
                    │ (AWS/Azure)   │
                    └───────────────┘
```

### Development Environment

- Local PostgreSQL database
- Development ECDSA keys (not for production)
- Hot reload for all components
- Integrated testing environment

## Monitoring and Observability

### Metrics
- License generation rate
- Download statistics
- Application crash reports
- Performance metrics

### Logging
- Structured logging with correlation IDs
- Security event logging
- Error tracking and alerting
- Audit trails for sensitive operations

### Health Checks
- Database connectivity
- HSM/KMS availability
- Application health endpoints
- Automated failover procedures

## Compliance and Security

### Standards
- SOC 2 Type II compliance
- GDPR compliance for EU users
- Regular security audits
- Penetration testing

### Data Protection
- Encryption at rest and in transit
- Minimal data collection
- Right to deletion
- Data breach response procedures

## Scalability Considerations

### Horizontal Scaling
- Stateless license service design
- Database read replicas
- CDN for static assets
- Container orchestration ready

### Performance Optimization
- Database indexing strategy
- Caching layers (Redis)
- Async processing for webhooks
- Rate limiting and DDoS protection

## Disaster Recovery

### Backup Strategy
- Automated database backups
- Cross-region replication
- HSM key backup procedures
- Application state recovery

### Recovery Procedures
- RTO: 4 hours
- RPO: 1 hour
- Documented runbooks
- Regular DR testing
