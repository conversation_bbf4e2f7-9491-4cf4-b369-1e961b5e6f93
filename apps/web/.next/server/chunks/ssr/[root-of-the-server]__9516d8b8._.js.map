{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Shield, Github, Twitter, Mail } from 'lucide-react';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <Shield className=\"h-8 w-8 text-primary-400\" />\n              <span className=\"text-xl font-bold\">DataLock</span>\n            </Link>\n            <p className=\"text-gray-300 mb-4 max-w-md\">\n              Secure, offline-first password manager with zero-knowledge architecture. \n              Your passwords, your device, your control.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://github.com/datalock\"\n                className=\"text-gray-400 hover:text-white transition-colors\"\n                aria-label=\"GitHub\"\n              >\n                <Github className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"https://twitter.com/datalock\"\n                className=\"text-gray-400 hover:text-white transition-colors\"\n                aria-label=\"Twitter\"\n              >\n                <Twitter className=\"h-5 w-5\" />\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"text-gray-400 hover:text-white transition-colors\"\n                aria-label=\"Email\"\n              >\n                <Mail className=\"h-5 w-5\" />\n              </a>\n            </div>\n          </div>\n\n          {/* Product */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-200 uppercase tracking-wider mb-4\">\n              Product\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"#features\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"#security\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Security\n                </Link>\n              </li>\n              <li>\n                <Link href=\"#pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Pricing\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/download\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Download\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-200 uppercase tracking-wider mb-4\">\n              Support\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/docs\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Documentation\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  FAQ\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Contact\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  Privacy Policy\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © {new Date().getFullYear()} DataLock. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link href=\"/terms\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n              Terms of Service\n            </Link>\n            <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n              Privacy Policy\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmD;;;;;;;;;;;sDAItF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmD;;;;;;;;;;;sDAItF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQ1F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;sDAIjF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAwB;gCAChC,IAAI,OAAO,WAAW;gCAAG;;;;;;;sCAE9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA2D;;;;;;8CAGzF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvG", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatPrice(price: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(price);\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,KAAK;IACzD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n  asChild?: boolean;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, asChild, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    \n    const variants = {\n      primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-primary-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base',\n    };\n\n    if (asChild) {\n      return (\n        <span\n          className={cn(\n            baseClasses,\n            variants[variant],\n            sizes[size],\n            className\n          )}\n        >\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChG,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;sBAGD;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/sections/hero-section.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Shield, Download, Lock, Zap } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport function HeroSection() {\n  return (\n    <section className=\"bg-gradient-to-br from-primary-50 to-white py-20 lg:py-32\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-8\">\n            <Shield className=\"h-4 w-4 mr-2\" />\n            Zero-Knowledge Architecture\n          </div>\n\n          {/* Headline */}\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Your Passwords,\n            <br />\n            <span className=\"text-primary-600\">Your Device,</span>\n            <br />\n            Your Control\n          </h1>\n\n          {/* Subheadline */}\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            DataLock is a secure, offline-first password manager that keeps your data \n            encrypted on your device. No cloud storage, no data breaches, no compromises.\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n            <Button size=\"lg\" asChild>\n              <Link href=\"#pricing\">\n                Get Started Free\n              </Link>\n            </Button>\n            <Button variant=\"outline\" size=\"lg\" asChild>\n              <Link href=\"/download\" className=\"flex items-center\">\n                <Download className=\"h-5 w-5 mr-2\" />\n                Download Now\n              </Link>\n            </Button>\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-gray-500\">\n            <div className=\"flex items-center\">\n              <Lock className=\"h-4 w-4 mr-2 text-green-500\" />\n              AES-256 Encryption\n            </div>\n            <div className=\"flex items-center\">\n              <Zap className=\"h-4 w-4 mr-2 text-blue-500\" />\n              Offline-First\n            </div>\n            <div className=\"flex items-center\">\n              <Shield className=\"h-4 w-4 mr-2 text-purple-500\" />\n              Open Source\n            </div>\n          </div>\n        </div>\n\n        {/* Hero Image/Demo */}\n        <div className=\"mt-16 relative\">\n          <div className=\"bg-white rounded-2xl shadow-2xl border border-gray-200 p-8 max-w-4xl mx-auto\">\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"flex space-x-2\">\n                  <div className=\"w-3 h-3 bg-red-400 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-yellow-400 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                </div>\n                <div className=\"flex-1 text-center\">\n                  <span className=\"text-sm text-gray-600 font-medium\">DataLock</span>\n                </div>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <div className=\"bg-white rounded-lg p-4 flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-blue-600 font-semibold text-sm\">G</span>\n                    </div>\n                    <div>\n                      <div className=\"font-medium text-gray-900\">Gmail</div>\n                      <div className=\"text-sm text-gray-500\"><EMAIL></div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-gray-400\">••••••••</div>\n                </div>\n                \n                <div className=\"bg-white rounded-lg p-4 flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-green-600 font-semibold text-sm\">S</span>\n                    </div>\n                    <div>\n                      <div className=\"font-medium text-gray-900\">Spotify</div>\n                      <div className=\"text-sm text-gray-500\">john.doe</div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-gray-400\">••••••••</div>\n                </div>\n                \n                <div className=\"bg-white rounded-lg p-4 flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-purple-600 font-semibold text-sm\">N</span>\n                    </div>\n                    <div>\n                      <div className=\"font-medium text-gray-900\">Netflix</div>\n                      <div className=\"text-sm text-gray-500\"><EMAIL></div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-gray-400\">••••••••</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAKrC,8OAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,8OAAC;;;;;gCAAK;;;;;;;sCAKR,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAM5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iJAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;8CAIxB,8OAAC,iJAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,OAAO;8CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;0DAC/B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;sCAO3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAgC;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;8CAGhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;;;;;;8CAIxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;sEAExD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAuC;;;;;;;;;;;sEAEzD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAwC;;;;;;;;;;;sEAE1D,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/sections/features-section.tsx"], "sourcesContent": ["import { \n  Shield, \n  Smartphone, \n  Zap, \n  Key, \n  Download, \n  Lock,\n  Eye,\n  RefreshCw \n} from 'lucide-react';\n\nconst features = [\n  {\n    icon: Shield,\n    title: 'Zero-Knowledge Architecture',\n    description: 'Your vault data never leaves your device unencrypted. We can\\'t see your passwords even if we wanted to.',\n  },\n  {\n    icon: Zap,\n    title: 'Offline-First Design',\n    description: 'Works completely offline. No internet required for daily use. Your passwords are always accessible.',\n  },\n  {\n    icon: Lock,\n    title: 'Military-Grade Encryption',\n    description: 'AES-256-GCM encryption with Argon2id key derivation. The same encryption used by governments.',\n  },\n  {\n    icon: Smartphone,\n    title: 'Multi-Device Support',\n    description: 'Use on Windows, macOS, and Linux. Sync via your own secure methods or keep devices independent.',\n  },\n  {\n    icon: Key,\n    title: 'Strong Password Generator',\n    description: 'Generate cryptographically secure passwords with customizable length and character sets.',\n  },\n  {\n    icon: Eye,\n    title: 'Secure Sharing',\n    description: 'Share passwords securely with encrypted exports. No cloud storage, no data leaks.',\n  },\n  {\n    icon: RefreshCw,\n    title: 'Automatic Backups',\n    description: 'Create encrypted local backups. Your data stays on your devices, under your control.',\n  },\n  {\n    icon: Download,\n    title: 'Import/Export',\n    description: 'Easily migrate from other password managers with secure import and export features.',\n  },\n];\n\nexport function FeaturesSection() {\n  return (\n    <section id=\"features\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Built for Security and Privacy\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            DataLock combines cutting-edge security with user-friendly design. \n            Every feature is designed with your privacy and security in mind.\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300\"\n            >\n              <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary-200 transition-colors\">\n                <feature.icon className=\"h-6 w-6 text-primary-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 text-sm leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to take control of your passwords?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Join thousands of users who trust DataLock to keep their digital lives secure.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"#pricing\"\n                className=\"bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium\"\n              >\n                View Pricing\n              </a>\n              <a\n                href=\"/download\"\n                className=\"border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n              >\n                Try Free Version\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAWA,MAAM,WAAW;IACf;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAVjB;;;;;;;;;;8BAiBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/sections/security-section.tsx"], "sourcesContent": ["import { Shield, Lock, Eye, Server, Key, AlertTriangle } from 'lucide-react';\n\nconst securityFeatures = [\n  {\n    icon: Lock,\n    title: 'AES-256-GCM Encryption',\n    description: 'Industry-standard encryption that would take billions of years to crack with current technology.',\n  },\n  {\n    icon: Key,\n    title: 'Argon2id Key Derivation',\n    description: 'Memory-hard key derivation function that resists both GPU and ASIC attacks.',\n  },\n  {\n    icon: Eye,\n    title: 'Zero-Knowledge Architecture',\n    description: 'We never see your master password or vault data. Everything is encrypted locally.',\n  },\n  {\n    icon: Server,\n    title: 'No Cloud Storage',\n    description: 'Your encrypted vault stays on your devices. No cloud servers to hack or breach.',\n  },\n];\n\nconst threatProtections = [\n  'Brute force attacks',\n  'Data breaches',\n  'Man-in-the-middle attacks',\n  'Keyloggers and malware',\n  'Physical device theft',\n  'Government surveillance',\n];\n\nexport function SecuritySection() {\n  return (\n    <section id=\"security\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-red-100 text-red-700 text-sm font-medium mb-6\">\n            <AlertTriangle className=\"h-4 w-4 mr-2\" />\n            Security First\n          </div>\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Bank-Level Security for Your Passwords\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            DataLock uses the same encryption standards as banks and governments. \n            Your passwords are protected by multiple layers of security.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Security Features */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              Military-Grade Protection\n            </h3>\n            <div className=\"space-y-6\">\n              {securityFeatures.map((feature, index) => (\n                <div key={index} className=\"flex items-start space-x-4\">\n                  <div className=\"w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <feature.icon className=\"h-5 w-5 text-primary-600\" />\n                  </div>\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                      {feature.title}\n                    </h4>\n                    <p className=\"text-gray-600\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Threat Protection */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              Protected Against\n            </h3>\n            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\n              <div className=\"space-y-4\">\n                {threatProtections.map((threat, index) => (\n                  <div key={index} className=\"flex items-center space-x-3\">\n                    <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n                      <Shield className=\"h-4 w-4 text-green-600\" />\n                    </div>\n                    <span className=\"text-gray-700\">{threat}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Security Guarantee */}\n            <div className=\"mt-8 p-6 bg-primary-50 rounded-xl border border-primary-200\">\n              <h4 className=\"text-lg font-semibold text-primary-900 mb-2\">\n                Our Security Promise\n              </h4>\n              <p className=\"text-primary-700 text-sm\">\n                If your master password is lost, your data cannot be recovered - not by us, \n                not by anyone. This is by design and ensures your complete privacy.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Security Audit Info */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-200 max-w-4xl mx-auto\">\n            <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n              Independently Audited\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              DataLock's security has been independently audited by leading cybersecurity firms. \n              Our code is open source and available for review by the security community.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"/security-audit\"\n                className=\"text-primary-600 hover:text-primary-700 font-medium\"\n              >\n                View Security Audit →\n              </a>\n              <a\n                href=\"https://github.com/datalock\"\n                className=\"text-primary-600 hover:text-primary-700 font-medium\"\n              >\n                View Source Code →\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,mBAAmB;IACvB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG5C,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;2CAThB;;;;;;;;;;;;;;;;sCAkBhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDAAK,WAAU;kEAAiB;;;;;;;+CAJzB;;;;;;;;;;;;;;;8CAWhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAG5D,8OAAC;4CAAE,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;8BAS9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/sections/pricing-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PricingSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingSection() from the server but PricingSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/sections/pricing-section.tsx <module evaluation>\",\n    \"PricingSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sFACA", "debugId": null}}, {"offset": {"line": 1568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/components/sections/pricing-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PricingSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingSection() from the server but PricingSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/sections/pricing-section.tsx\",\n    \"PricingSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/DataLock/apps/web/src/app/page.tsx"], "sourcesContent": ["import { Header } from '@/components/layout/header';\nimport { Footer } from '@/components/layout/footer';\nimport { HeroSection } from '@/components/sections/hero-section';\nimport { FeaturesSection } from '@/components/sections/features-section';\nimport { SecuritySection } from '@/components/sections/security-section';\nimport { PricingSection } from '@/components/sections/pricing-section';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Header />\n      <main className=\"flex-1\">\n        <HeroSection />\n        <FeaturesSection />\n        <SecuritySection />\n        <PricingSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,qJAAA,CAAA,SAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,gKAAA,CAAA,cAAW;;;;;kCACZ,8OAAC,oKAAA,CAAA,kBAAe;;;;;kCAChB,8OAAC,oKAAA,CAAA,kBAAe;;;;;kCAChB,8OAAC,mKAAA,CAAA,iBAAc;;;;;;;;;;;0BAEjB,8OAAC,qJAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}