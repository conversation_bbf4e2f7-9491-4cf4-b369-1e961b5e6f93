import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "DataLock - Secure Password Manager",
  description: "Secure, offline-first password manager with zero-knowledge architecture. Your passwords, your device, your control.",
  keywords: ["password manager", "security", "encryption", "offline", "privacy"],
  authors: [{ name: "DataLock Team" }],
  openGraph: {
    title: "DataLock - Secure Password Manager",
    description: "Secure, offline-first password manager with zero-knowledge architecture.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "DataLock - Secure Password Manager",
    description: "Secure, offline-first password manager with zero-knowledge architecture.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased bg-white text-gray-900`}>
        {children}
      </body>
    </html>
  );
}
