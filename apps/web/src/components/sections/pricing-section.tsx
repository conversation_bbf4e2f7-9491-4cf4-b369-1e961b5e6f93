'use client';

import { useState } from 'react';
import { Check, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatPrice } from '@/lib/utils';

const plans = {
  lifetime: [
    {
      name: 'Personal',
      price: 49,
      devices: 1,
      popular: false,
      features: [
        'Unlimited passwords',
        'Secure password generator',
        'Local encrypted storage',
        'Import/Export',
        'Desktop app',
        'Lifetime updates',
      ],
    },
    {
      name: 'Family',
      price: 99,
      devices: 3,
      popular: true,
      features: [
        'Everything in Personal',
        'Up to 3 devices',
        'Secure sharing',
        'Family vault management',
        'Priority support',
        'Lifetime updates',
      ],
    },
    {
      name: 'Team',
      price: 199,
      devices: 10,
      popular: false,
      features: [
        'Everything in Family',
        'Up to 10 devices',
        'Team management',
        'Advanced sharing',
        'Admin controls',
        'Lifetime updates',
      ],
    },
  ],
  annual: [
    {
      name: 'Personal',
      price: 19,
      devices: 1,
      popular: false,
      features: [
        'Unlimited passwords',
        'Secure password generator',
        'Local encrypted storage',
        'Import/Export',
        'Desktop app',
        'Annual updates',
      ],
    },
    {
      name: 'Family',
      price: 39,
      devices: 3,
      popular: true,
      features: [
        'Everything in Personal',
        'Up to 3 devices',
        'Secure sharing',
        'Family vault management',
        'Priority support',
        'Annual updates',
      ],
    },
    {
      name: 'Team',
      price: 79,
      devices: 10,
      popular: false,
      features: [
        'Everything in Family',
        'Up to 10 devices',
        'Team management',
        'Advanced sharing',
        'Admin controls',
        'Annual updates',
      ],
    },
  ],
};

export function PricingSection() {
  const [billingType, setBillingType] = useState<'lifetime' | 'annual'>('lifetime');

  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Choose the plan that fits your needs. All plans include the same security features.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setBillingType('lifetime')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingType === 'lifetime'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Lifetime
            </button>
            <button
              onClick={() => setBillingType('annual')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingType === 'annual'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Annual
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans[billingType].map((plan, index) => (
            <div
              key={index}
              className={`relative rounded-2xl border-2 p-8 ${
                plan.popular
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="inline-flex items-center px-4 py-1 rounded-full bg-primary-500 text-white text-sm font-medium">
                    <Star className="h-4 w-4 mr-1" />
                    Most Popular
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <div className="mb-2">
                  <span className="text-4xl font-bold text-gray-900">
                    {formatPrice(plan.price)}
                  </span>
                  {billingType === 'annual' && (
                    <span className="text-gray-600 ml-1">/year</span>
                  )}
                </div>
                <p className="text-gray-600">
                  Up to {plan.devices} device{plan.devices > 1 ? 's' : ''}
                </p>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                className="w-full"
                variant={plan.popular ? 'primary' : 'outline'}
                size="lg"
              >
                Get Started
              </Button>
            </div>
          ))}
        </div>

        {/* FAQ */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                What's the difference between lifetime and annual?
              </h4>
              <p className="text-gray-600 text-sm">
                Lifetime plans are a one-time purchase with lifetime updates. 
                Annual plans require yearly renewal but cost less upfront.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Can I upgrade my plan later?
              </h4>
              <p className="text-gray-600 text-sm">
                Yes, you can upgrade to a higher tier at any time. 
                We'll credit your existing purchase toward the new plan.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                Is there a free trial?
              </h4>
              <p className="text-gray-600 text-sm">
                Yes, you can download and use DataLock for free with basic features. 
                Upgrade when you're ready for advanced features.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                What payment methods do you accept?
              </h4>
              <p className="text-gray-600 text-sm">
                We accept all major credit cards, PayPal, and local payment methods 
                depending on your region.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
