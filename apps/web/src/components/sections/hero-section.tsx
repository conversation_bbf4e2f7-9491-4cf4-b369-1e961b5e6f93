import Link from 'next/link';
import { Shield, Download, Lock, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function HeroSection() {
  return (
    <section className="bg-gradient-to-br from-primary-50 to-white py-20 lg:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium mb-8">
            <Shield className="h-4 w-4 mr-2" />
            Zero-Knowledge Architecture
          </div>

          {/* Headline */}
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Your Passwords,
            <br />
            <span className="text-primary-600">Your Device,</span>
            <br />
            Your Control
          </h1>

          {/* Subheadline */}
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            DataLock is a secure, offline-first password manager that keeps your data 
            encrypted on your device. No cloud storage, no data breaches, no compromises.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" asChild>
              <Link href="#pricing">
                Get Started Free
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/download" className="flex items-center">
                <Download className="h-5 w-5 mr-2" />
                Download Now
              </Link>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center">
              <Lock className="h-4 w-4 mr-2 text-green-500" />
              AES-256 Encryption
            </div>
            <div className="flex items-center">
              <Zap className="h-4 w-4 mr-2 text-blue-500" />
              Offline-First
            </div>
            <div className="flex items-center">
              <Shield className="h-4 w-4 mr-2 text-purple-500" />
              Open Source
            </div>
          </div>
        </div>

        {/* Hero Image/Demo */}
        <div className="mt-16 relative">
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 p-8 max-w-4xl mx-auto">
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="flex-1 text-center">
                  <span className="text-sm text-gray-600 font-medium">DataLock</span>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-sm">G</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Gmail</div>
                      <div className="text-sm text-gray-500"><EMAIL></div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-400">••••••••</div>
                </div>
                
                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-sm">S</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Spotify</div>
                      <div className="text-sm text-gray-500">john.doe</div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-400">••••••••</div>
                </div>
                
                <div className="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-sm">N</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Netflix</div>
                      <div className="text-sm text-gray-500"><EMAIL></div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-400">••••••••</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
