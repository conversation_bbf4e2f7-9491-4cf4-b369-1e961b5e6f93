import { Shield, Lock, Eye, Server, Key, AlertTriangle } from 'lucide-react';

const securityFeatures = [
  {
    icon: Lock,
    title: 'AES-256-GCM Encryption',
    description: 'Industry-standard encryption that would take billions of years to crack with current technology.',
  },
  {
    icon: Key,
    title: 'Argon2id Key Derivation',
    description: 'Memory-hard key derivation function that resists both GPU and ASIC attacks.',
  },
  {
    icon: Eye,
    title: 'Zero-Knowledge Architecture',
    description: 'We never see your master password or vault data. Everything is encrypted locally.',
  },
  {
    icon: Server,
    title: 'No Cloud Storage',
    description: 'Your encrypted vault stays on your devices. No cloud servers to hack or breach.',
  },
];

const threatProtections = [
  'Brute force attacks',
  'Data breaches',
  'Man-in-the-middle attacks',
  'Keyloggers and malware',
  'Physical device theft',
  'Government surveillance',
];

export function SecuritySection() {
  return (
    <section id="security" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-red-100 text-red-700 text-sm font-medium mb-6">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Security First
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Bank-Level Security for Your Passwords
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            DataLock uses the same encryption standards as banks and governments. 
            Your passwords are protected by multiple layers of security.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Security Features */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Military-Grade Protection
            </h3>
            <div className="space-y-6">
              {securityFeatures.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <feature.icon className="h-5 w-5 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      {feature.title}
                    </h4>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Threat Protection */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Protected Against
            </h3>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="space-y-4">
                {threatProtections.map((threat, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <Shield className="h-4 w-4 text-green-600" />
                    </div>
                    <span className="text-gray-700">{threat}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Security Guarantee */}
            <div className="mt-8 p-6 bg-primary-50 rounded-xl border border-primary-200">
              <h4 className="text-lg font-semibold text-primary-900 mb-2">
                Our Security Promise
              </h4>
              <p className="text-primary-700 text-sm">
                If your master password is lost, your data cannot be recovered - not by us, 
                not by anyone. This is by design and ensures your complete privacy.
              </p>
            </div>
          </div>
        </div>

        {/* Security Audit Info */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 max-w-4xl mx-auto">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Independently Audited
            </h3>
            <p className="text-gray-600 mb-6">
              DataLock's security has been independently audited by leading cybersecurity firms. 
              Our code is open source and available for review by the security community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/security-audit"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View Security Audit →
              </a>
              <a
                href="https://github.com/datalock"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View Source Code →
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
