import { 
  Shield, 
  Smartphone, 
  Zap, 
  Key, 
  Download, 
  Lock,
  Eye,
  RefreshCw 
} from 'lucide-react';

const features = [
  {
    icon: Shield,
    title: 'Zero-Knowledge Architecture',
    description: 'Your vault data never leaves your device unencrypted. We can\'t see your passwords even if we wanted to.',
  },
  {
    icon: Zap,
    title: 'Offline-First Design',
    description: 'Works completely offline. No internet required for daily use. Your passwords are always accessible.',
  },
  {
    icon: Lock,
    title: 'Military-Grade Encryption',
    description: 'AES-256-GCM encryption with Argon2id key derivation. The same encryption used by governments.',
  },
  {
    icon: Smartphone,
    title: 'Multi-Device Support',
    description: 'Use on Windows, macOS, and Linux. Sync via your own secure methods or keep devices independent.',
  },
  {
    icon: Key,
    title: 'Strong Password Generator',
    description: 'Generate cryptographically secure passwords with customizable length and character sets.',
  },
  {
    icon: Eye,
    title: 'Secure Sharing',
    description: 'Share passwords securely with encrypted exports. No cloud storage, no data leaks.',
  },
  {
    icon: RefreshCw,
    title: 'Automatic Backups',
    description: 'Create encrypted local backups. Your data stays on your devices, under your control.',
  },
  {
    icon: Download,
    title: 'Import/Export',
    description: 'Easily migrate from other password managers with secure import and export features.',
  },
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Built for Security and Privacy
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            DataLock combines cutting-edge security with user-friendly design. 
            Every feature is designed with your privacy and security in mind.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300"
            >
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary-200 transition-colors">
                <feature.icon className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to take control of your passwords?
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of users who trust DataLock to keep their digital lives secure.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#pricing"
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium"
              >
                View Pricing
              </a>
              <a
                href="/download"
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Try Free Version
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
