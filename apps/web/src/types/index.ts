// DataLock Shared Types

export interface Plan {
  id: string;
  name: string;
  display_name: string;
  license_type: 'lifetime' | 'annual';
  device_limit: number;
  price_usd: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  email: string;
  name?: string;
  country?: string;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  customer_id: string;
  provider: 'stripe' | 'iyzico' | 'paytr';
  provider_tx_id: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  amount: number;
  currency: string;
  plan_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface License {
  id: string;
  customer_id: string;
  transaction_id?: string;
  plan_id: string;
  license_type: 'lifetime' | 'annual';
  device_limit: number;
  issued_at: string;
  expires_at?: string;
  revoked_at?: string;
  revoke_reason?: string;
  token_blob: LicenseToken;
  created_at: string;
  updated_at: string;
}

export interface LicenseToken {
  license_id: string;
  holder_email: string;
  plan: string;
  license_type: 'lifetime' | 'annual';
  issued_at: string;
  expires_at?: string;
  device_limit: number;
  features: string[];
  version: number;
  signature: string;
}

// API Request/Response Types

export interface PaymentWebhookRequest {
  provider: 'stripe' | 'iyzico' | 'paytr';
  event: string;
  email: string;
  amount: number;
  currency: string;
  tx_id: string;
  plan: string;
  metadata?: Record<string, any>;
}

export interface LicenseIssueRequest {
  email: string;
  plan: string;
  device_limit?: number;
  expires_at?: string;
}

export interface LicenseIssueResponse {
  license_token: string;
  download_url: string;
}

// Utility Types

export type LicenseStatus = 'active' | 'expired' | 'revoked';
export type Platform = 'windows' | 'macos' | 'linux';
export type Environment = 'development' | 'staging' | 'production';
