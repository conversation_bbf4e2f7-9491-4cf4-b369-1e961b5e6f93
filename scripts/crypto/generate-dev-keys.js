#!/usr/bin/env node

/**
 * Development Key Generation Script
 * 
 * Generates ECDSA P-256 key pairs for development and testing.
 * 
 * ⚠️  WARNING: These keys are for development only!
 *     Never use in production environments.
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

const KEYS_DIR = path.join(__dirname, '../../keys');
const DEV_PRIVATE_KEY_FILE = path.join(KEYS_DIR, 'dev-private-key.pem');
const DEV_PUBLIC_KEY_FILE = path.join(KEYS_DIR, 'dev-public-key.pem');

function ensureKeysDirectory() {
  if (!fs.existsSync(KEYS_DIR)) {
    fs.mkdirSync(KEYS_DIR, { recursive: true });
    console.log(`Created keys directory: ${KEYS_DIR}`);
  }
}

function generateKeyPair() {
  console.log('Generating ECDSA P-256 key pair...');
  
  const { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
    namedCurve: 'prime256v1', // P-256
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });
  
  return { publicKey, privateKey };
}

function saveKeys(publicKey, privateKey) {
  // Save private key
  fs.writeFileSync(DEV_PRIVATE_KEY_FILE, privateKey, { mode: 0o600 });
  console.log(`Private key saved: ${DEV_PRIVATE_KEY_FILE}`);
  
  // Save public key
  fs.writeFileSync(DEV_PUBLIC_KEY_FILE, publicKey);
  console.log(`Public key saved: ${DEV_PUBLIC_KEY_FILE}`);
}

function generateKeyInfo(publicKey, privateKey) {
  // Extract key information
  const publicKeyObj = crypto.createPublicKey(publicKey);
  const privateKeyObj = crypto.createPrivateKey(privateKey);
  
  const keyInfo = {
    algorithm: 'ECDSA',
    curve: 'P-256',
    keySize: 256,
    generated: new Date().toISOString(),
    environment: 'development',
    warning: 'FOR DEVELOPMENT USE ONLY - DO NOT USE IN PRODUCTION',
    publicKeyPem: publicKey,
    // Note: We don't include private key in info file for security
  };
  
  const keyInfoFile = path.join(KEYS_DIR, 'dev-key-info.json');
  fs.writeFileSync(keyInfoFile, JSON.stringify(keyInfo, null, 2));
  console.log(`Key info saved: ${keyInfoFile}`);
}

function createGitignore() {
  const gitignoreFile = path.join(KEYS_DIR, '.gitignore');
  const gitignoreContent = `# Never commit private keys to git
*.pem
*.key
*.p12
*.pfx
dev-*
!.gitignore
`;
  
  fs.writeFileSync(gitignoreFile, gitignoreContent);
  console.log(`Created .gitignore: ${gitignoreFile}`);
}

function testKeyPair(publicKey, privateKey) {
  console.log('Testing key pair...');
  
  const testData = 'DataLock license test signature';
  
  // Sign test data
  const sign = crypto.createSign('SHA256');
  sign.update(testData);
  const signature = sign.sign(privateKey);
  
  // Verify signature
  const verify = crypto.createVerify('SHA256');
  verify.update(testData);
  const isValid = verify.verify(publicKey, signature);
  
  if (isValid) {
    console.log('✅ Key pair test successful');
  } else {
    console.error('❌ Key pair test failed');
    process.exit(1);
  }
}

function main() {
  console.log('🔐 DataLock Development Key Generator');
  console.log('=====================================');
  console.log();
  console.log('⚠️  WARNING: These keys are for development only!');
  console.log('   Never use in production environments.');
  console.log();
  
  // Check if keys already exist
  if (fs.existsSync(DEV_PRIVATE_KEY_FILE) || fs.existsSync(DEV_PUBLIC_KEY_FILE)) {
    console.log('Development keys already exist.');
    console.log('Delete existing keys if you want to generate new ones:');
    console.log(`  rm ${DEV_PRIVATE_KEY_FILE}`);
    console.log(`  rm ${DEV_PUBLIC_KEY_FILE}`);
    return;
  }
  
  try {
    ensureKeysDirectory();
    
    const { publicKey, privateKey } = generateKeyPair();
    
    saveKeys(publicKey, privateKey);
    generateKeyInfo(publicKey, privateKey);
    createGitignore();
    testKeyPair(publicKey, privateKey);
    
    console.log();
    console.log('✅ Development keys generated successfully!');
    console.log();
    console.log('Next steps:');
    console.log('1. Update license service configuration');
    console.log('2. Update desktop app with public key');
    console.log('3. Test license signing and verification');
    
  } catch (error) {
    console.error('❌ Key generation failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { generateKeyPair, testKeyPair };
