name: Release

on:
  push:
    tags:
      - 'v*'

env:
  NODE_VERSION: '18'
  RUST_VERSION: 'stable'

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      version: ${{ steps.get_version.outputs.version }}
    steps:
      - name: Get version from tag
        id: get_version
        run: echo "version=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: DataLock v${{ steps.get_version.outputs.version }}
          draft: true
          prerelease: false

  build-and-sign:
    name: Build and Sign
    needs: create-release
    strategy:
      matrix:
        include:
          - platform: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            ext: AppImage
          - platform: windows-latest
            target: x86_64-pc-windows-msvc
            ext: msi
          - platform: macos-latest
            target: x86_64-apple-darwin
            ext: dmg
          - platform: macos-latest
            target: aarch64-apple-darwin
            ext: dmg
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.RUST_VERSION }}
          targets: ${{ matrix.target }}

      - name: Install system dependencies (Ubuntu)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install dependencies
        run: npm ci

      - name: Build desktop app
        working-directory: apps/desktop
        env:
          TAURI_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
          TAURI_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}
        run: npm run tauri build -- --target ${{ matrix.target }}

      - name: Code sign (Windows)
        if: matrix.platform == 'windows-latest'
        env:
          WINDOWS_CERTIFICATE: ${{ secrets.WINDOWS_CERTIFICATE }}
          WINDOWS_CERTIFICATE_PASSWORD: ${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}
        run: |
          # Windows code signing logic here
          echo "Code signing for Windows"

      - name: Notarize (macOS)
        if: matrix.platform == 'macos-latest'
        env:
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_SIGNING_IDENTITY: ${{ secrets.APPLE_SIGNING_IDENTITY }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
        run: |
          # macOS notarization logic here
          echo "Notarizing for macOS"

      - name: Generate checksums
        run: |
          cd apps/desktop/src-tauri/target/${{ matrix.target }}/release/bundle
          find . -name "*.${{ matrix.ext }}" -exec sha256sum {} \; > checksums.txt

      - name: Upload release assets
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: apps/desktop/src-tauri/target/${{ matrix.target }}/release/bundle/
          asset_name: datalock-${{ needs.create-release.outputs.version }}-${{ matrix.target }}.${{ matrix.ext }}
          asset_content_type: application/octet-stream
