#!/usr/bin/env node

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration from environment
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'datalock',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
};

async function runSeeds() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('Connected to database');
    
    // Get list of seed files
    const seedsDir = path.join(__dirname, '../seeds');
    const seedFiles = fs.readdirSync(seedsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    console.log('Available seed files:', seedFiles);
    
    // Run each seed file
    for (const seedFile of seedFiles) {
      const seedPath = path.join(seedsDir, seedFile);
      const sql = fs.readFileSync(seedPath, 'utf8');
      
      console.log(`Running seed: ${seedFile}`);
      
      await client.query('BEGIN');
      try {
        await client.query(sql);
        await client.query('COMMIT');
        console.log(`✓ Completed seed: ${seedFile}`);
      } catch (error) {
        await client.query('ROLLBACK');
        console.error(`❌ Failed to run seed ${seedFile}:`, error.message);
        // Continue with other seeds
      }
    }
    
    console.log('✅ All seeds completed');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run seeds
if (require.main === module) {
  runSeeds();
}

module.exports = { runSeeds };
