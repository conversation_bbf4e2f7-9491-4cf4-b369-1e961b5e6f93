#!/usr/bin/env node

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration from environment
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'datalock',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
};

async function createMigrationsTable(client) {
  await client.query(`
    CREATE TABLE IF NOT EXISTS schema_migrations (
      version VARCHAR(255) PRIMARY KEY,
      applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
  `);
}

async function getAppliedMigrations(client) {
  const result = await client.query(
    'SELECT version FROM schema_migrations ORDER BY version'
  );
  return result.rows.map(row => row.version);
}

async function applyMigration(client, migrationFile) {
  const migrationPath = path.join(__dirname, '../migrations', migrationFile);
  const sql = fs.readFileSync(migrationPath, 'utf8');
  
  console.log(`Applying migration: ${migrationFile}`);
  
  await client.query('BEGIN');
  try {
    await client.query(sql);
    
    // Extract version from filename (e.g., "001_initial_schema.sql" -> "001")
    const version = migrationFile.split('_')[0];
    await client.query(
      'INSERT INTO schema_migrations (version) VALUES ($1)',
      [version]
    );
    
    await client.query('COMMIT');
    console.log(`✓ Applied migration: ${migrationFile}`);
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  }
}

async function runMigrations() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('Connected to database');
    
    // Create migrations table if it doesn't exist
    await createMigrationsTable(client);
    
    // Get list of applied migrations
    const appliedMigrations = await getAppliedMigrations(client);
    console.log('Applied migrations:', appliedMigrations);
    
    // Get list of migration files
    const migrationsDir = path.join(__dirname, '../migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    console.log('Available migrations:', migrationFiles);
    
    // Apply pending migrations
    for (const migrationFile of migrationFiles) {
      const version = migrationFile.split('_')[0];
      
      if (!appliedMigrations.includes(version)) {
        await applyMigration(client, migrationFile);
      } else {
        console.log(`⏭ Skipping already applied migration: ${migrationFile}`);
      }
    }
    
    console.log('✅ All migrations completed successfully');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run migrations
if (require.main === module) {
  runMigrations();
}

module.exports = { runMigrations };
