{"name": "@datalock/database", "version": "0.1.0", "private": true, "description": "Database schemas and migrations for DataLock", "main": "index.js", "scripts": {"migrate": "node scripts/migrate.js", "migrate:up": "node scripts/migrate.js up", "migrate:down": "node scripts/migrate.js down", "seed": "node scripts/seed.js", "reset": "node scripts/reset.js", "generate": "node scripts/generate-migration.js"}, "dependencies": {"pg": "^8.11.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/pg": "^8.10.0", "@types/uuid": "^9.0.0"}}