# DataLock Environment Configuration Template
# Copy this file to .env.local and fill in your values

# =============================================================================
# Database Configuration
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=datalock
DB_USER=postgres
DB_PASSWORD=postgres
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/datalock

# =============================================================================
# License Service Configuration
# =============================================================================
SECRET_KEY=your-secret-key-here-change-in-production
ENVIRONMENT=development
DEBUG=true

# ECDSA Key Configuration
# For development: use generated keys in keys/ directory
# For production: use HSM/KMS
ECDSA_PRIVATE_KEY_PATH=./keys/dev-private-key.pem
ECDSA_PUBLIC_KEY_PATH=./keys/dev-public-key.pem

# HSM/KMS Configuration (Production only)
# AWS_ACCESS_KEY_ID=AKIA...
# AWS_SECRET_ACCESS_KEY=...
# AWS_REGION=us-east-1
# KMS_KEY_ID=arn:aws:kms:us-east-1:123456789012:key/...

# =============================================================================
# Payment Provider Configuration
# =============================================================================

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Alternative: Iyzico (for Turkish market)
# IYZICO_API_KEY=...
# IYZICO_SECRET_KEY=...
# IYZICO_BASE_URL=https://sandbox-api.iyzipay.com

# Alternative: PayTR (for Turkish market)
# PAYTR_MERCHANT_ID=...
# PAYTR_MERCHANT_KEY=...
# PAYTR_MERCHANT_SALT=...

# =============================================================================
# Email Service Configuration
# =============================================================================

# Postmark
POSTMARK_API_TOKEN=...
FROM_EMAIL=<EMAIL>

# Alternative: AWS SES
# AWS_SES_REGION=us-east-1
# AWS_SES_ACCESS_KEY_ID=...
# AWS_SES_SECRET_ACCESS_KEY=...

# =============================================================================
# Web Platform Configuration
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
NEXT_PUBLIC_ENVIRONMENT=development

# =============================================================================
# Security Configuration
# =============================================================================

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://datalock.com

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_TIMEOUT_HOURS=24

# =============================================================================
# Monitoring and Logging
# =============================================================================

# Sentry (Error Tracking)
# SENTRY_DSN=https://...@sentry.io/...

# Log Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# File Storage Configuration
# =============================================================================

# Local storage for development
STORAGE_TYPE=local
STORAGE_PATH=./uploads

# Production: AWS S3
# STORAGE_TYPE=s3
# AWS_S3_BUCKET=datalock-releases
# AWS_S3_REGION=us-east-1
# AWS_S3_ACCESS_KEY_ID=...
# AWS_S3_SECRET_ACCESS_KEY=...

# =============================================================================
# CDN Configuration
# =============================================================================

# Cloudflare
# CLOUDFLARE_ZONE_ID=...
# CLOUDFLARE_API_TOKEN=...

# =============================================================================
# Development Tools
# =============================================================================

# Hot reload for development
HOT_RELOAD=true

# Test database (for CI/CD)
TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/datalock_test

# =============================================================================
# Production Deployment
# =============================================================================

# Domain configuration
# DOMAIN=datalock.com
# API_DOMAIN=api.datalock.com

# SSL/TLS
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# Load balancer health check
# HEALTH_CHECK_TOKEN=your-health-check-token
