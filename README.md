# DataLock - Secure Password Manager

DataLock is a secure, offline-first password manager with serverless vault storage and offline license validation.

## Architecture Overview

- **Web Platform**: Purchase → License Generation → Download Links (vault data never reaches server)
- **License Service**: Post-payment webhook generates signed license tokens, offline validation in app
- **Desktop Application**: Single master password unlocks local encrypted vault, data encrypted with AEAD
- **Optional Recovery Kit**: Serverless recovery system

## Project Structure

```
DataLock/
├── apps/
│   ├── web/                    # Next.js website (landing, purchase, downloads)
│   ├── license-service/        # FastAPI backend (webhooks, license generation)
│   └── desktop/               # Tauri app (React + Rust core)
├── packages/
│   ├── crypto/                # Shared cryptography utilities
│   ├── database/              # Database schemas and migrations
│   └── types/                 # Shared TypeScript types
├── docs/                      # Documentation and architecture
├── scripts/                   # Build and deployment scripts
└── .github/                   # CI/CD workflows
```

## Security Principles

- **Zero-Knowledge**: Vault data never leaves user's device
- **Offline-First**: License validation works completely offline
- **Master Password Lost = Data Lost**: No server-side recovery by design
- **Signed Updates**: All application updates are cryptographically signed

## Getting Started

See individual app directories for specific setup instructions:
- [Web Platform](./apps/web/README.md)
- [License Service](./apps/license-service/README.md)  
- [Desktop App](./apps/desktop/README.md)

## License

Proprietary - All rights reserved
