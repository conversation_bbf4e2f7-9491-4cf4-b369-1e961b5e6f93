{"name": "datalock", "version": "0.1.0", "private": true, "description": "Secure, offline-first password manager", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "desktop:dev": "cd apps/desktop && npm run tauri dev", "desktop:build": "cd apps/desktop && npm run tauri build", "web:dev": "cd apps/web && npm run dev", "license-service:dev": "cd apps/license-service && python -m uvicorn main:app --reload", "db:migrate": "cd packages/database && npm run migrate", "db:seed": "cd packages/database && npm run seed", "crypto:generate-dev-keys": "node scripts/crypto/generate-dev-keys.js", "crypto:verify-keys": "node scripts/crypto/verify-keys.js", "crypto:sign-test-license": "node scripts/crypto/sign-test-license.js"}, "devDependencies": {"@types/node": "^20.0.0", "prettier": "^3.0.0", "turbo": "^1.10.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/datalock.git"}, "keywords": ["password-manager", "security", "encryption", "offline-first", "tauri", "rust"], "author": "DataLock Team", "license": "PROPRIETARY"}